#!/usr/bin/env python3
"""
Migration script to move extractor and timeline collections to unified workspace collections.

This script:
1. Processes all workspaces in the system (or specific workspace if provided)
2. Migrates extractor and timeline collections to unified workspace collections
3. Cleans up old collections immediately after migration
4. Updates database configurations

Usage:
    python scripts/migrate_unified_collections.py [--dry-run] [--workspace-id ID]
"""

import argparse
import asyncio
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple, Union
from qdrant_client import models as qdrant_models

from ira_chat.db import api as db_api, models, base
from ira_chat.services import vectorstore as ira_vectorstore, langchain_svc, engine_manager

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


async def migrate_all_workspaces(dry_run: bool = True) -> Dict[str, Any]:
    """
    Migrate all workspaces' extractor/timeline collections to unified collections.
    
    Args:
        dry_run: If True, only log what would be done without making changes
        
    Returns:
        Dict with migration summary
    """
    logger.info(f"Starting migration for ALL workspaces (dry_run={dry_run})")
    
    # Get all organizations
    orgs = await db_api.list_orgs_all()
    logger.info(f"Found {len(orgs)} organizations")
    
    total_summary = {
        'workspaces_processed': 0,
        'total_documents_migrated': 0,
        'collections_migrated': 0,
        'errors': []
    }
    
    for org in orgs:
        # Get all workspaces for this org
        workspaces, _ = await db_api.list_workspaces(org_id=org.id)
        logger.info(f"Processing {len(workspaces)} workspaces for org: {org.name}")
        
        for workspace in workspaces:
            try:
                result = await migrate_workspace_to_unified_collections(
                    workspace.id, org, dry_run
                )
                total_summary['workspaces_processed'] += 1
                total_summary['total_documents_migrated'] += result['total_documents']
                total_summary['collections_migrated'] += result['collections_migrated']
                
            except Exception as e:
                error_msg = f"Error migrating workspace {workspace.id} ({workspace.name}): {str(e)}"
                logger.error(error_msg)
                total_summary['errors'].append(error_msg)
                # Fail fast on significant errors
                if "connection" in str(e).lower() or "database" in str(e).lower():
                    raise
    
    logger.info(f"Migration completed. Summary: {total_summary}")
    return total_summary


async def collect_extractor_timeline_datasets(workspace_id: int) -> Tuple[Dict[str, Union[models.Extractor, Tuple[models.Timeline, models.Extractor]]], List[str]]:
    """
    Collect and map all extractor and timeline datasets for a workspace.

    Args:
        workspace_id: ID of workspace to process

    Returns:
        Tuple of (extractor_map, dataset_ids_list) where:
        - extractor_map: dataset_id -> extractor or (timeline, extractor)
        - dataset_ids_list: list of dataset IDs that need migration
    """
    # Find all extractors and timelines for this workspace
    extractors, _ = await db_api.list_extractors(workspace_id=workspace_id)
    timelines, _ = await db_api.list_timelines(workspace_id=workspace_id)

    logger.info(f"Found {len(extractors)} extractors and {len(timelines)} timelines")

    # Collect all dataset IDs that need migration
    dataset_ids = set()
    extractor_map = {}  # dataset_id -> extractor or (timeline, extractor)

    # Process regular extractors
    for extractor in extractors:
        if hasattr(extractor, 'auto_dataset_id') and extractor.auto_dataset_id:
            dataset_ids.add(extractor.auto_dataset_id)
            extractor_map[extractor.auto_dataset_id] = extractor

    # Process timeline extractors
    for timeline in timelines:
        timeline_points, _ = await db_api.list_timeline_points(timeline_id=timeline.id)
        for point in timeline_points:
            extractor = await db_api.get_extractor_by_id(point.extractor_id)
            if hasattr(extractor, 'auto_dataset_id') and extractor.auto_dataset_id:
                dataset_ids.add(extractor.auto_dataset_id)
                extractor_map[extractor.auto_dataset_id] = (timeline, extractor)

    return extractor_map, list(dataset_ids)


async def initialize_unified_collection(org: models.Organization, workspace: models.Workspace, dry_run: bool = True) -> Tuple[Any, str]:
    """
    Initialize vectorstore and prepare unified collection.

    Args:
        org: Organization object
        workspace: Workspace object
        dry_run: If True, skip actual collection creation

    Returns:
        Tuple of (vectorstore, unified_collection_name)
    """
    unified_collection_name = f"{org.name}-{workspace.name}"
    logger.info(f"Target unified collection: {unified_collection_name}")

    # Initialize vectorstore
    org_config = org.get_config()
    vectorstore = ira_vectorstore.get_vectorstore('langchain', org_config, org.id, workspace.id)

    # Create unified collection if needed
    if not dry_run:
        unified_namespace = ira_vectorstore.get_index_namespace(unified_collection_name)
        langchain_svc.prepare_vectorstore(vectorstore, unified_namespace, index_recreate=False)
        logger.info(f"Prepared unified collection: {unified_collection_name}")

    return vectorstore, unified_collection_name


async def orchestrate_dataset_migration(
    datasets: List[models.Dataset],
    extractor_map: Dict[str, Union[models.Extractor, Tuple[models.Timeline, models.Extractor]]],
    unified_collection_name: str,
    vectorstore: Any,
    dry_run: bool = True
) -> Tuple[int, int]:
    """
    Orchestrate the migration of all datasets to unified collection.

    Args:
        datasets: List of datasets to migrate
        extractor_map: Mapping of dataset_id to extractor info
        unified_collection_name: Name of target unified collection
        vectorstore: Vectorstore instance
        dry_run: If True, only count documents without migrating

    Returns:
        Tuple of (total_documents_migrated, collections_migrated)
    """
    total_documents = 0
    collections_migrated = 0

    for dataset in datasets:
        indexes = await db_api.list_dataset_indexes(dataset_id=dataset.id)
        for index in indexes:
            extractor_info = extractor_map.get(dataset.id)
            if extractor_info:
                docs_migrated = await migrate_index_to_unified(
                    index, dataset, unified_collection_name, extractor_info, vectorstore, dry_run
                )
                total_documents += docs_migrated
                collections_migrated += 1

    return total_documents, collections_migrated


async def migrate_workspace_to_unified_collections(
    workspace_id: int, 
    org: Optional[models.Organization] = None, 
    dry_run: bool = True
) -> Dict[str, Any]:
    """
    Migrate a workspace's extractor/timeline collections to unified collection.
    
    Args:
        workspace_id: ID of workspace to migrate
        org: Organization object (optional, will be fetched if not provided)
        dry_run: If True, only log what would be done without making changes
        
    Returns:
        Dict with migration results
    """
    logger.info(f"Starting migration for workspace {workspace_id} (dry_run={dry_run})")

    # Get workspace and organization info
    workspace = await db_api.get_workspace_by_id(workspace_id)
    if not org:
        org = await db_api.get_org_by_id(workspace.org_id)

    # Collect extractor and timeline datasets
    extractor_map, dataset_ids = await collect_extractor_timeline_datasets(workspace_id)

    if not dataset_ids:
        logger.info("No datasets found for migration")
        return {'total_documents': 0, 'collections_migrated': 0}

    # Get datasets to migrate
    datasets = await get_datasets_by_ids(dataset_ids)
    logger.info(f"Found {len(datasets)} datasets to migrate")

    # Initialize vectorstore and unified collection
    vectorstore, unified_collection_name = await initialize_unified_collection(org, workspace, dry_run)

    # Orchestrate the migration process
    total_documents, collections_migrated = await orchestrate_dataset_migration(
        datasets, extractor_map, unified_collection_name, vectorstore, dry_run
    )
    
    # Update database configurations
    if not dry_run:
        index_ids = []
        for dataset in datasets:
            indexes = await db_api.list_dataset_indexes(dataset_id=dataset.id)
            index_ids.extend([idx.id for idx in indexes])
        
        await update_indexes_for_unified_collections(index_ids)
    
    logger.info(f"Workspace {workspace_id} migration completed. Documents: {total_documents}, Collections: {collections_migrated}")
    return {'total_documents': total_documents, 'collections_migrated': collections_migrated}


async def validate_source_collection_and_count_documents(
    index: models.DatasetIndex,
    dataset: models.Dataset,
    vectorstore: Any
) -> Tuple[str, str, int]:
    """
    Validate source collection exists and count documents.

    Args:
        index: DatasetIndex to validate
        dataset: Associated Dataset
        vectorstore: Vectorstore instance

    Returns:
        Tuple of (source_collection_name, source_collection_full, document_count)

    Raises:
        Exception: If collection doesn't exist or can't be accessed
    """
    # Get organization for this dataset
    org = await db_api.get_org_by_id(dataset.org_id)

    # Determine source collection name using existing logic
    source_collection_name = engine_manager.index_namespace_suffix(
        org,
        dataset,
        index.name
    )
    source_collection_full = ira_vectorstore.get_index_namespace(source_collection_name)

    client = vectorstore.client

    # Check if source collection exists
    try:
        if not client.collection_exists(source_collection_full):
            logger.warning(f"Source collection {source_collection_full} does not exist")
            return source_collection_name, source_collection_full, 0

        # Count documents
        collection_info = client.get_collection(source_collection_full)
        document_count = collection_info.points_count
        logger.info(f"Found {document_count} documents in {source_collection_full}")

        return source_collection_name, source_collection_full, document_count

    except Exception as e:
        logger.error(f"Error accessing source collection {source_collection_full}: {str(e)}")
        raise


async def process_document_batch_with_metadata_enhancement(
    points: List[Any],
    extractor_info: Union[models.Extractor, Tuple[models.Timeline, models.Extractor]],
    dataset: models.Dataset
) -> List[qdrant_models.PointStruct]:
    """
    Process a batch of documents and enhance their metadata for unified collection.

    Args:
        points: List of document points from source collection
        extractor_info: Extractor or (Timeline, Extractor) tuple
        dataset: Associated Dataset

    Returns:
        List of enhanced PointStruct objects
    """
    enhanced_points = []

    for point in points:
        enhanced_metadata = point.payload.get('metadata', {}).copy()

        # Add filtering metadata based on extractor type
        if isinstance(extractor_info, tuple):
            # Timeline extractor
            timeline, extractor = extractor_info
            enhanced_metadata.update({
                'timeline_id': timeline.id,
                'extractor_id': extractor.id,
                'subcollection_type': 'timeline',
                'subcollection_id': f'timeline-{timeline.id}',
                'workspace_id': dataset.workspace_id
            })
        else:
            # Regular extractor
            extractor = extractor_info
            enhanced_metadata.update({
                'extractor_id': extractor.id,
                'subcollection_type': 'extractor',
                'subcollection_id': f'extractor-{extractor.id}',
                'workspace_id': dataset.workspace_id
            })

        # Create enhanced point
        enhanced_point = qdrant_models.PointStruct(
            id=point.id,
            vector=point.vector,
            payload={
                **point.payload,
                'metadata': enhanced_metadata
            }
        )
        enhanced_points.append(enhanced_point)

    return enhanced_points


async def upload_documents_and_cleanup(
    client: Any,
    source_collection_full: str,
    unified_collection_full: str,
    enhanced_points: List[qdrant_models.PointStruct],
    migrated_count: int,
    document_count: int,
    cleanup_after_migration: bool = True
) -> int:
    """
    Upload enhanced documents to unified collection and optionally cleanup source.

    Args:
        client: Qdrant client
        source_collection_full: Full name of source collection
        unified_collection_full: Full name of unified collection
        enhanced_points: List of enhanced document points
        migrated_count: Current count of migrated documents
        document_count: Total expected document count
        cleanup_after_migration: Whether to delete source collection after migration

    Returns:
        Updated migrated_count

    Raises:
        Exception: If upload fails or migration validation fails
    """
    # Validate enhanced points before upload
    if not enhanced_points:
        logger.warning(f"No enhanced points created for batch")
        return migrated_count

    # Upload enhanced points to unified collection
    try:
        client.upsert(
            collection_name=unified_collection_full,
            points=enhanced_points
        )
    except Exception as e:
        logger.error(f"Error uploading batch to unified collection: {str(e)}")
        raise

    migrated_count += len(enhanced_points)
    progress_pct = (migrated_count / document_count * 100) if document_count > 0 else 100
    logger.info(f"Migrated {migrated_count}/{document_count} documents ({progress_pct:.1f}%)")

    # If this is the final batch, validate and cleanup
    if migrated_count >= document_count and cleanup_after_migration:
        # Validate migration was successful
        if migrated_count != document_count:
            logger.error(f"Migration incomplete: expected {document_count}, migrated {migrated_count}")
            raise ValueError(f"Migration failed: document count mismatch")

        # Clean up old collection immediately after successful migration
        try:
            client.delete_collection(source_collection_full)
            logger.info(f"Deleted old collection: {source_collection_full}")
        except Exception as e:
            # Non-fatal error - log and continue
            logger.warning(f"Failed to delete old collection {source_collection_full}: {str(e)}")

    return migrated_count


async def migrate_index_to_unified(
        index: models.DatasetIndex,
        dataset: models.Dataset,
        unified_collection_name: str,
        extractor_info: Union[models.Extractor, Tuple[models.Timeline, models.Extractor]],
        vectorstore: Any,
        dry_run: bool = True
) -> int:
    """
    Migrate a single index to unified collection with enhanced metadata.

    Args:
        index: DatasetIndex to migrate
        dataset: Associated Dataset
        unified_collection_name: Target unified collection name
        extractor_info: Extractor or (Timeline, Extractor) tuple
        vectorstore: Vectorstore instance for migration
        dry_run: If True, only count documents without migrating

    Returns:
        Number of documents migrated
    """
    logger.info(f"Migrating index {index.name} from dataset {dataset.name}")

    # Validate source collection and count documents
    source_collection_name, source_collection_full, document_count = await validate_source_collection_and_count_documents(
        index, dataset, vectorstore
    )

    if document_count == 0:
        return 0

    if dry_run:
        return document_count

    # Migrate documents in batches
    migrated_count = 0
    offset = None
    batch_size = 1000
    client = vectorstore.client
    unified_collection_full = ira_vectorstore.get_index_namespace(unified_collection_name)

    while True:
        # Retrieve batch of documents
        points, next_offset = client.scroll(
            collection_name=source_collection_full,
            limit=batch_size,
            with_payload=True,
            with_vectors=True,
            offset=offset
        )

        if not points:
            break

        # Process batch with metadata enhancement
        enhanced_points = await process_document_batch_with_metadata_enhancement(
            points, extractor_info, dataset
        )

        # Upload documents and handle cleanup if this is the final batch
        is_final_batch = (migrated_count + len(enhanced_points)) >= document_count
        migrated_count = await upload_documents_and_cleanup(
            client, source_collection_full, unified_collection_full, enhanced_points,
            migrated_count, document_count, cleanup_after_migration=is_final_batch
        )

        offset = next_offset
        if not offset:
            break

    logger.info(f"Successfully migrated {migrated_count} documents from {source_collection_full}")
    return migrated_count


async def update_indexes_for_unified_collections(index_ids: List[str]):
    """
    Update DatasetIndex configurations to use unified collections.

    Args:
        index_ids: List of DatasetIndex IDs to update
    """
    logger.info(f"Updating {len(index_ids)} indexes for unified collections")

    for index_id in index_ids:
        index = await db_api.get_dataset_index(index_id)
        current_config = index.get_config()

        # Update config to use unified collection
        updated_config = current_config.copy()
        updated_config.update({
            'use_unified_collection': True,
            'collection_type': 'UNIFIED_WORKSPACE',
            'migrated_at': datetime.utcnow().isoformat()
        })

        await db_api.update_dataset_index(index, {'config': updated_config})
        logger.info(f"Updated index {index_id} configuration for unified collection")


# Helper functions
async def get_datasets_by_ids(dataset_ids: List[str]) -> List[models.Dataset]:
    """Get datasets by their IDs."""
    datasets = []
    for dataset_id in dataset_ids:
        dataset = await db_api.get_dataset(dataset_id)
        datasets.append(dataset)
    return datasets


async def main():
    """Main migration entry point."""
    parser = argparse.ArgumentParser(description='Migrate extractors/timelines to unified collections')
    parser.add_argument('--workspace-id', type=int, help='Specific workspace ID to migrate (optional)')
    parser.add_argument('--dry-run', action='store_true', help='Perform dry run without making changes')

    args = parser.parse_args()

    async with base.session_context():
        if args.workspace_id:
            # Migrate specific workspace
            logger.info(f"Migrating specific workspace: {args.workspace_id}")
            result = await migrate_workspace_to_unified_collections(args.workspace_id, dry_run=args.dry_run)
            logger.info(f"Migration result: {result}")
        else:
            # Migrate all workspaces
            logger.info("Migrating all workspaces")
            result = await migrate_all_workspaces(dry_run=args.dry_run)
            logger.info(f"Migration summary: {result}")


if __name__ == '__main__':
    asyncio.run(main())
